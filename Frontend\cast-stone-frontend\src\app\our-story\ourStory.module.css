/* Our Story Page Styles - Magazine/Editorial Theme */
.OurStoryroot {
  --cast-stone-brown: #4a3728;
  --cast-stone-light-brown: #6b4e3d;
  --cast-stone-cream: #faf9f7;
  --cast-stone-white: #ffffff;
  --cast-stone-gray: #8b7355;
  --cast-stone-shadow: rgba(74, 55, 40, 0.1);
  --cast-stone-shadow-hover: rgba(74, 55, 40, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Page Container */
.storyPage {
  min-height: 100vh;
  background-color: var(--cast-stone-white);
}

/* Hero Section */
.heroSection {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #8b4513 100%);
  padding: 8rem 0 6rem;
  position: relative;
  overflow: hidden;
}

.heroSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.heroContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.heroTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 4rem;
  font-weight: 700;
  color: var(--cast-stone-white);
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
  animation: fadeInUp 1s ease-out forwards;
}

.heroSubtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.25rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out 0.3s forwards;
  opacity: 0;
  transform: translateY(30px);
}

/* Main Content */
.mainContent {
  padding: 6rem 0;
  background-color: var(--cast-stone-white);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Titles */
.sectionTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--cast-stone-brown);
  text-align: center;
  margin-bottom: 3rem;
  letter-spacing: -0.01em;
  position: relative;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  border-radius: 2px;
}

/* Introduction Section */
.introSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 6rem;
  padding: 4rem 0;
}

.introContent {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.introText {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--cast-stone-gray);
  text-align: justify;
}

.introImage {
  position: relative;
}

.imagePlaceholder {
  width: 100%;
  height: 400px;
  background: linear-gradient(135deg, var(--cast-stone-cream), #f0ede8);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(74, 55, 40, 0.1);
  position: relative;
  overflow: hidden;
}

.imagePlaceholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="texture" width="20" height="20" patternUnits="userSpaceOnUse"><rect width="20" height="20" fill="rgba(74,55,40,0.02)"/><circle cx="10" cy="10" r="1" fill="rgba(74,55,40,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23texture)"/></svg>');
}

.imageText {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.2rem;
  color: var(--cast-stone-brown);
  font-weight: 600;
  position: relative;
  z-index: 2;
}

/* Heritage Section */
.heritageSection {
  margin-bottom: 6rem;
  padding: 4rem 0;
  background: var(--cast-stone-cream);
  border-radius: 24px;
}

.heritageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.heritageCard {
  background: var(--cast-stone-white);
  padding: 2.5rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 24px var(--cast-stone-shadow);
  transition: var(--transition-smooth);
  border: 1px solid rgba(74, 55, 40, 0.08);
}

.heritageCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 40px var(--cast-stone-shadow-hover);
}

.heritageIcon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: var(--cast-stone-white);
}

.heritageTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--cast-stone-brown);
  margin-bottom: 1rem;
}

.heritageText {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--cast-stone-gray);
}

/* Timeline Section */
.timelineSection {
  margin-bottom: 6rem;
  padding: 4rem 0;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  transform: translateX(-50%);
}

.timelineItem {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  position: relative;
}

.timelineItem:nth-child(odd) {
  flex-direction: row;
}

.timelineItem:nth-child(even) {
  flex-direction: row-reverse;
}

.timelineYear {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--cast-stone-white);
  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  padding: 1rem 1.5rem;
  border-radius: 50px;
  min-width: 100px;
  text-align: center;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 12px var(--cast-stone-shadow);
}

.timelineContent {
  flex: 1;
  background: var(--cast-stone-white);
  padding: 2rem;
  border-radius: 16px;
  margin: 0 2rem;
  box-shadow: 0 8px 24px var(--cast-stone-shadow);
  border: 1px solid rgba(74, 55, 40, 0.08);
}

.timelineTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--cast-stone-brown);
  margin-bottom: 0.5rem;
}

.timelineText {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--cast-stone-gray);
}

/* Values Section */
.valuesSection {
  margin-bottom: 6rem;
  padding: 4rem 0;
  background: var(--cast-stone-cream);
  border-radius: 24px;
}

.valuesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.valueCard {
  background: var(--cast-stone-white);
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--cast-stone-shadow);
  transition: var(--transition-smooth);
  border: 1px solid rgba(74, 55, 40, 0.08);
}

.valueCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px var(--cast-stone-shadow-hover);
}

.valueTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--cast-stone-brown);
  margin-bottom: 1rem;
}

.valueText {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--cast-stone-gray);
}

/* Call to Action Section */
.ctaSection {
  text-align: center;
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  border-radius: 24px;
  color: var(--cast-stone-white);
  position: relative;
  overflow: hidden;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-grain)"/></svg>');
  opacity: 0.3;
}

.ctaContent {
  position: relative;
  z-index: 2;
}

.ctaTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

.ctaText {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButtons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton,
.secondaryButton {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  padding: 1.25rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  min-width: 200px;
  text-align: center;
}

.primaryButton {
  background: var(--cast-stone-white);
  color: var(--cast-stone-brown);
  border: 2px solid var(--cast-stone-white);
}

.primaryButton:hover {
  background: transparent;
  color: var(--cast-stone-white);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.secondaryButton {
  background: transparent;
  color: var(--cast-stone-white);
  border: 2px solid var(--cast-stone-white);
}

.secondaryButton:hover {
  background: var(--cast-stone-white);
  color: var(--cast-stone-brown);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .introSection {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .heroTitle {
    font-size: 3rem;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .heroSection {
    padding: 6rem 0 4rem;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .mainContent {
    padding: 4rem 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .timeline::before {
    left: 2rem;
  }
  
  .timelineItem {
    flex-direction: row !important;
    padding-left: 4rem;
  }
  
  .timelineYear {
    position: absolute;
    left: 0;
    min-width: 80px;
    font-size: 1.2rem;
    padding: 0.75rem 1rem;
  }
  
  .timelineContent {
    margin-left: 2rem;
    margin-right: 0;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .heroSubtitle {
    font-size: 1rem;
  }
  
  .sectionTitle {
    font-size: 1.75rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .heritageCard,
  .valueCard,
  .timelineContent {
    padding: 1.5rem;
  }
}
