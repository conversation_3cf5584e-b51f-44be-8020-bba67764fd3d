.container {
  min-height: 100vh;
  background: #1a1a2e;
  color: white;
  padding: 2rem 1rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  line-height: 1.6;
}

.content {
  max-width: 1000px;
  margin: 0 auto;
}

.comingSoon {
  text-align: center;
  padding: 4rem 2rem;
  background: #2a2a3e;
  border-radius: 16px;
  border: 2px solid #3a3a4e;
}

.icon {
  color: #4a90e2;
  margin-bottom: 2rem;
}

.comingSoon h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.comingSoon > p {
  font-size: 1.1rem;
  color: #b0b0b0;
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature {
  background: #3a3a4e;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #4a4a5e;
}

.feature h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #4a90e2;
}

.feature p {
  color: #b0b0b0;
  line-height: 1.5;
}

.contactInfo {
  background: #3a3a4e;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #4a4a5e;
  margin-top: 2rem;
}

.contactInfo h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #4a90e2;
}

.contactInfo > p {
  color: #b0b0b0;
  margin-bottom: 1.5rem;
}

.contactDetails {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contactItem {
  color: #e0e0e0;
  font-size: 1rem;
}

.contactItem strong {
  color: white;
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .comingSoon h2 {
    font-size: 2rem;
  }
  
  .features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .container {
    padding: 1rem;
  }
  
  .comingSoon {
    padding: 2rem 1rem;
  }
  
  .contactDetails {
    text-align: left;
  }
}
